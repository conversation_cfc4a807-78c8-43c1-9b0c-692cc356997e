<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>稳定对齐测试</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .status {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .gantt-wrapper {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            height: 500px;
        }
        
        .debug-line {
            position: fixed;
            height: 2px;
            z-index: 10000;
            pointer-events: none;
            opacity: 0.7;
        }
        
        .debug-line::after {
            content: attr(data-label);
            position: absolute;
            right: 5px;
            top: -20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 稳定对齐测试</h1>
        <p>验证表格与甘特图的对齐效果</p>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="checkAlignment()">检查对齐</button>
        <button class="btn" onclick="clearDebugLines()">清除调试线</button>
    </div>
    
    <div id="status" class="status">
        正在初始化甘特图...
    </div>
    
    <div class="gantt-wrapper">
        <svg id="gantt"></svg>
    </div>

    <script src="../dist/frappe-gantt.umd.js"></script>
    <script>
        let gantt;
        
        // 简单的测试数据
        const tasks = [
            {
                id: 'task1',
                name: '任务一',
                start: '2024-01-05',
                end: '2024-01-12',
                progress: 100
            },
            {
                id: 'task2',
                name: '任务二',
                start: '2024-01-08',
                end: '2024-01-15',
                progress: 60
            },
            {
                id: 'task3',
                name: '任务三',
                start: '2024-01-10',
                end: '2024-01-20',
                progress: 30
            },
            {
                id: 'task4',
                name: '任务四',
                start: '2024-01-15',
                end: '2024-01-25',
                progress: 0
            }
        ];
        
        function initGantt() {
            try {
                console.log('开始初始化甘特图...');
                console.log('任务数据:', tasks);
                
                // 验证任务数据
                for (let i = 0; i < tasks.length; i++) {
                    const task = tasks[i];
                    if (!task.name) {
                        throw new Error(`任务 ${i} 缺少 name 属性`);
                    }
                    if (!task.start || !task.end) {
                        throw new Error(`任务 ${i} 缺少 start 或 end 属性`);
                    }
                }
                
                const GanttConstructor = window.Gantt || window.FrappeGantt;
                if (!GanttConstructor) {
                    throw new Error('找不到 Gantt 构造函数');
                }
                
                console.log('Gantt 构造函数:', GanttConstructor);
                
                gantt = new GanttConstructor('#gantt', tasks, {
                    header_height: 120,
                    column_width: 30,
                    step: 24,
                    bar_height: 20,
                    padding: 50,
                    view_mode: 'Day',
                    table_tree: {
                        enabled: true,
                        show_table: true,
                        table_width: 200,
                        columns: [
                            { key: 'name', label: '任务名称', width: 200 }
                        ]
                    }
                });
                
                console.log('甘特图初始化成功');
                document.getElementById('status').innerHTML = '✅ 甘特图初始化成功，点击"检查对齐"开始测试';
                document.getElementById('status').className = 'status success';
                
                // 自动检查对齐
                setTimeout(checkAlignment, 1000);
                
            } catch (error) {
                console.error('甘特图初始化失败:', error);
                document.getElementById('status').innerHTML = '❌ 甘特图初始化失败: ' + error.message;
                document.getElementById('status').className = 'status error';
            }
        }
        
        function checkAlignment() {
            try {
                console.log('开始检查对齐...');
                
                const tableRows = document.querySelectorAll('.table-row');
                const ganttBars = document.querySelectorAll('.bar-wrapper');
                
                console.log('表格行数:', tableRows.length);
                console.log('甘特图任务条数:', ganttBars.length);
                
                if (tableRows.length === 0 || ganttBars.length === 0) {
                    console.log('等待元素加载...');
                    setTimeout(checkAlignment, 500);
                    return;
                }

                let maxDiff = 0;
                let alignmentIssues = [];

                clearDebugLines();

                for (let i = 0; i < Math.min(tableRows.length, ganttBars.length); i++) {
                    const tableRow = tableRows[i];
                    const ganttBar = ganttBars[i];
                    
                    const tableRect = tableRow.getBoundingClientRect();
                    const tableCenterY = tableRect.top + tableRect.height / 2;
                    
                    const barRect = ganttBar.getBoundingClientRect();
                    const barCenterY = barRect.top + barRect.height / 2;
                    
                    const diff = Math.abs(tableCenterY - barCenterY);
                    maxDiff = Math.max(maxDiff, diff);
                    
                    console.log(`任务 ${i}: 表格Y=${tableCenterY.toFixed(1)}, 甘特Y=${barCenterY.toFixed(1)}, 差异=${diff.toFixed(1)}px`);
                    
                    if (diff > 1) {
                        alignmentIssues.push({
                            index: i,
                            tableCenterY,
                            barCenterY,
                            diff
                        });
                    }
                    
                    // 绘制调试线
                    drawDebugLine(0, tableCenterY, window.innerWidth, tableCenterY, 'red', `表格${i}`);
                    drawDebugLine(0, barCenterY, window.innerWidth, barCenterY, 'blue', `甘特${i}`);
                }

                updateStatus(maxDiff, alignmentIssues);
                
            } catch (error) {
                console.error('检查对齐失败:', error);
                document.getElementById('status').innerHTML = '❌ 检查对齐失败: ' + error.message;
                document.getElementById('status').className = 'status error';
            }
        }
        
        function drawDebugLine(x1, y1, x2, y2, color, label) {
            const line = document.createElement('div');
            line.className = 'debug-line';
            line.style.left = x1 + 'px';
            line.style.top = y1 + 'px';
            line.style.width = (x2 - x1) + 'px';
            line.style.backgroundColor = color;
            line.setAttribute('data-label', label);
            document.body.appendChild(line);
        }
        
        function clearDebugLines() {
            document.querySelectorAll('.debug-line').forEach(line => line.remove());
        }
        
        function updateStatus(maxDiff, issues) {
            const statusEl = document.getElementById('status');
            
            if (maxDiff <= 1) {
                statusEl.className = 'status success';
                statusEl.innerHTML = '🎉 对齐完美！最大差异: ' + maxDiff.toFixed(1) + 'px';
            } else {
                statusEl.className = 'status error';
                statusEl.innerHTML = '⚠️ 存在对齐问题，最大差异: ' + maxDiff.toFixed(1) + 'px';
                
                if (issues.length > 0) {
                    statusEl.innerHTML += '<br><br>问题详情:<br>';
                    issues.forEach(issue => {
                        statusEl.innerHTML += `任务${issue.index}: 差异${issue.diff.toFixed(1)}px<br>`;
                    });
                }
            }
        }
        
        // 页面加载完成后初始化甘特图
        window.addEventListener('load', function() {
            console.log('页面加载完成，开始初始化甘特图');
            initGantt();
        });
    </script>
</body>
</html>
